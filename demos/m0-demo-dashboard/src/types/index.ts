/**
 * Type Definitions Index for M0 Demo Dashboard
 * Purpose: Central export point for all type definitions
 */

// Governance Types
export type {
  IGovernanceRule,
  IComplianceMetrics,
  IAuditEntry,
  IAuthorityChain,
  IGTSKSystem,
  ICrossReference,
  IGovernanceComponent,
  IGovernanceRulesResponse,
  IComplianceResponse,
  IAuditTrailResponse,
  IAuthorityChainResponse,
  ICrossReferenceResponse,
  ICreateGovernanceRuleRequest,
  IUpdateGovernanceRuleRequest,
  IGovernanceFilters,
} from './governance.types';

// Tracking Types
export type {
  ITrackingService,
  IImplementationProgress,
  ISessionData,
  ISessionEvent,
  IAnalyticsCacheMetrics,
  IComponentStatus,
  IComponentAlert,
  IOrchestrationMetrics,
  ICoordinationEvent,
  ITrackingComponentsResponse,
  IImplementationProgressResponse,
  ISessionTrackingResponse,
  IAnalyticsPerformanceResponse,
  IComponentStatusResponse,
  ITrackingFilters,
} from './tracking.types';

// Security Types
export type {
  IMemorySafetyMetrics,
  IMemoryDataPoint,
  ISystemSecurity,
  IThreatDetection,
  IAttackSimulation,
  ISimulationLog,
  ISecurityAlert,
  IMemoryBoundaryConfig,
  IBaseTrackingServiceStatus,
  IMemoryUsageResponse,
  IAttackSimulationResponse,
  IProtectionStatusResponse,
  IBoundaryEnforcementResponse,
  ISecurityFilters,
} from './security.types';

// Demo Types
export type {
  IM0ComponentStatus,
  IHealthCheck,
  IM0FoundationCapability,
  IIntegrationTest,
  ITestLog,
  ITestAssertion,
  IDemoConfiguration,
  IDashboardMetrics,
  ISimulationControl,
  IEventCorrelation,
  IM0ComponentStatusResponse,
  IHealthCheckResponse,
  IM0FoundationStatusResponse,
  IIntegrationTestResponse,
  IEventCorrelationResponse,
  IDemoState,
  DashboardType,
  ComponentCategory,
  HealthStatus,
  OperationalStatus,
  TestStatus,
  SimulationStatus,
} from './demo.types';

// Common utility types
export type ApiResponse<T> = {
  data: T;
  success: boolean;
  message?: string;
  timestamp: string;
  errors?: string[];
};

export type PaginatedResponse<T> = {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
  totalPages: number;
};

export type FilterOptions = {
  search?: string;
  status?: string;
  category?: string;
  type?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
};

export type ChartDataPoint = {
  timestamp: string;
  value: number;
  label?: string;
  category?: string;
};

export type TimeSeriesData = {
  name: string;
  data: ChartDataPoint[];
  color?: string;
  unit?: string;
};

export type DashboardWidget = {
  id: string;
  title: string;
  type: 'chart' | 'metric' | 'table' | 'status' | 'alert';
  size: 'small' | 'medium' | 'large' | 'full';
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  data: unknown;
  refreshInterval?: number;
  lastUpdate?: string;
};

export type NotificationLevel = 'info' | 'success' | 'warning' | 'error';

export type Notification = {
  id: string;
  level: NotificationLevel;
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actions?: {
    label: string;
    action: () => void;
  }[];
};

// Environment and configuration types
export type EnvironmentConfig = {
  APP_NAME: string;
  APP_VERSION: string;
  M0_COMPONENTS_COUNT: number;
  GOVERNANCE_COMPONENTS: number;
  TRACKING_COMPONENTS: number;
  MEMORY_SAFETY_COMPONENTS: number;
  TOTAL_LOC: number;
  COMPLETION_PERCENTAGE: number;
  PROTECTED_SERVICES: number;
  MEMORY_MAPS: number;
  TYPESCRIPT_ERRORS: number;
  DEMO_MODE: boolean;
  ENHANCED_IMPLEMENTATION: boolean;
  REFRESH_INTERVAL: number;
  MEMORY_UPDATE_INTERVAL: number;
  GOVERNANCE_UPDATE_INTERVAL: number;
  TRACKING_UPDATE_INTERVAL: number;
  INTEGRATION_UPDATE_INTERVAL: number;
  M1_READY: boolean;
  M2_READY: boolean;
  EXTENSION_POINTS: number;
};

// Hook return types
export type UseRealTimeDataReturn<T> = {
  data: T | undefined;
  error: Error | undefined;
  isLoading: boolean;
  isValidating: boolean;
  mutate: () => void;
};

import type { ISimulationControl } from './demo.types';

export type UseSimulationReturn = {
  simulations: ISimulationControl[];
  activeSimulations: ISimulationControl[];
  startSimulation: (type: string, params: Record<string, unknown>) => Promise<void>;
  stopSimulation: (simulationId: string) => Promise<void>;
  clearSimulations: () => void;
};

// Form types
export type FormFieldType = 'text' | 'number' | 'select' | 'checkbox' | 'radio' | 'textarea' | 'date' | 'time';

export type FormField = {
  name: string;
  label: string;
  type: FormFieldType;
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    custom?: (value: unknown) => string | undefined;
  };
};

export type FormConfig = {
  fields: FormField[];
  submitLabel?: string;
  cancelLabel?: string;
  onSubmit: (data: Record<string, unknown>) => void;
  onCancel?: () => void;
  initialValues?: Record<string, unknown>;
};

// Error types
export type ErrorType = 'network' | 'validation' | 'authentication' | 'authorization' | 'server' | 'client' | 'unknown';

export type AppError = {
  type: ErrorType;
  message: string;
  details?: string;
  timestamp: string;
  component?: string;
  action?: string;
  recoverable: boolean;
};

// Theme types
export type ThemeMode = 'light' | 'dark' | 'auto';

export type ColorScheme = {
  primary: string;
  secondary: string;
  success: string;
  warning: string;
  error: string;
  info: string;
  background: string;
  surface: string;
  text: string;
};
